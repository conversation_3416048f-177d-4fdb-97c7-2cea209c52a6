
.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
}
.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
}
.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
}
.content-area {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
}
.title {
		font-size: 32rpx;
		margin-bottom: 40rpx;
		color: #333;
}
button {
		background-color: #007aff;
		color: white;
		border: none;
		padding: 24rpx 48rpx;
		border-radius: 12rpx;
		margin: 20rpx 0;
		font-size: 28rpx;
}
.info-box {
		margin-top: 40rpx;
		padding: 30rpx;
		background-color: white;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.info {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 16rpx;
		text-align: center;
		display: block;
}
