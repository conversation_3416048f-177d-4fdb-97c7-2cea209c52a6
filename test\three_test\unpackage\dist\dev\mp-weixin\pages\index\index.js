"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      message: "Animation Groups 演示",
      canvas: null,
      scene: null,
      camera: null,
      renderer: null,
      mixer: null,
      clock: null,
      animationId: null
    };
  },
  onLoad() {
    console.log("页面onLoad执行");
  },
  onReady() {
    console.log("页面onReady执行");
    console.log("当前平台:", common_vendor.index.getSystemInfoSync().platform);
    setTimeout(() => {
      this.initWebGL();
    }, 500);
  },
  onUnload() {
    if (this.animationId) {
      clearTimeout(this.animationId);
    }
  },
  methods: {
    initWebGL() {
      console.log("开始初始化Animation Groups演示");
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select("#webglCanvas").fields({
        node: true,
        size: true
      }).exec((res) => {
        console.log("Canvas查询结果:", res);
        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          console.log("Canvas节点获取成功:", canvas);
          const gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
          if (gl) {
            console.log("WebGL上下文获取成功!");
            this.canvas = canvas;
            this.setupThreeJS();
          } else {
            console.error("无法获取WebGL上下文");
            this.fallbackTo2D(canvas);
          }
        } else {
          console.error("无法找到canvas节点");
        }
      });
    },
    setupThreeJS() {
      console.log("设置Three.js动画组场景");
      try {
        const THREE = require("three");
        const canvas = this.canvas;
        const dpr = common_vendor.index.getSystemInfoSync().pixelRatio;
        const width = 300 * dpr;
        const height = 300 * dpr;
        canvas.width = width;
        canvas.height = height;
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0);
        this.camera = new THREE.PerspectiveCamera(40, width / height, 1, 1e3);
        this.camera.position.set(50, 50, 100);
        this.camera.lookAt(this.scene.position);
        this.renderer = new THREE.WebGLRenderer({
          canvas,
          antialias: true
        });
        this.renderer.setSize(width, height, false);
        this.createAnimationGroup(THREE);
        this.clock = new THREE.Clock();
        this.animate();
        console.log("Three.js动画组场景设置完成");
      } catch (error) {
        console.error("Three.js设置失败:", error);
        this.fallbackTo2D(this.canvas);
      }
    },
    createAnimationGroup(THREE) {
      console.log("创建动画组");
      const animationGroup = new THREE.AnimationObjectGroup();
      const geometry = new THREE.BoxGeometry(5, 5, 5);
      const material = new THREE.MeshBasicMaterial({ transparent: true });
      for (let i = 0; i < 5; i++) {
        for (let j = 0; j < 5; j++) {
          const mesh = new THREE.Mesh(geometry, material);
          mesh.position.x = 32 - 16 * i;
          mesh.position.y = 0;
          mesh.position.z = 32 - 16 * j;
          this.scene.add(mesh);
          animationGroup.add(mesh);
        }
      }
      const xAxis = new THREE.Vector3(1, 0, 0);
      const qInitial = new THREE.Quaternion().setFromAxisAngle(xAxis, 0);
      const qFinal = new THREE.Quaternion().setFromAxisAngle(xAxis, Math.PI);
      const quaternionKF = new THREE.QuaternionKeyframeTrack(
        ".quaternion",
        [0, 1, 2],
        [
          qInitial.x,
          qInitial.y,
          qInitial.z,
          qInitial.w,
          qFinal.x,
          qFinal.y,
          qFinal.z,
          qFinal.w,
          qInitial.x,
          qInitial.y,
          qInitial.z,
          qInitial.w
        ]
      );
      const colorKF = new THREE.ColorKeyframeTrack(
        ".material.color",
        [0, 1, 2],
        [1, 0, 0, 0, 1, 0, 0, 0, 1],
        THREE.InterpolateDiscrete
      );
      const opacityKF = new THREE.NumberKeyframeTrack(".material.opacity", [0, 1, 2], [1, 0, 1]);
      const clip = new THREE.AnimationClip("default", 3, [quaternionKF, colorKF, opacityKF]);
      this.mixer = new THREE.AnimationMixer(animationGroup);
      const clipAction = this.mixer.clipAction(clip);
      clipAction.play();
      console.log("动画组创建完成，包含25个立方体");
    },
    animate() {
      if (!this.renderer || !this.scene || !this.camera)
        return;
      const delta = this.clock.getDelta();
      if (this.mixer) {
        this.mixer.update(delta);
      }
      this.renderer.render(this.scene, this.camera);
      this.animationId = setTimeout(() => this.animate(), 16);
    },
    fallbackTo2D(canvas) {
      console.log("回退到2D Canvas演示");
      const ctx = canvas.getContext("2d");
      if (ctx) {
        const dpr = common_vendor.index.getSystemInfoSync().pixelRatio;
        canvas.width = 300 * dpr;
        canvas.height = 300 * dpr;
        ctx.scale(dpr, dpr);
        ctx.fillStyle = "#000";
        ctx.fillRect(0, 0, 300, 300);
        ctx.fillStyle = "#fff";
        ctx.font = "16px Arial";
        ctx.textAlign = "center";
        ctx.fillText("WebGL不可用", 150, 130);
        ctx.fillText("Animation Groups演示", 150, 150);
        ctx.fillText("需要WebGL支持", 150, 170);
        console.log("2D Canvas回退演示完成");
      }
    },
    onTouchStart(e) {
      console.log("触摸开始");
    },
    onTouchMove(e) {
      console.log("触摸移动");
    },
    onTouchEnd(e) {
      console.log("触摸结束");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    b: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    c: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
