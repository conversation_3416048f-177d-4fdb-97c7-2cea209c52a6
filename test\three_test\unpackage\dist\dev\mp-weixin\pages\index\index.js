"use strict";
const common_vendor = require("../../common/vendor.js");
const exampleInfo = {
  name: "misc_animation_groups",
  useLoaders: [],
  info: [
    [
      {
        tag: "a",
        link: "https://threejs.org",
        content: "three.js"
      },
      {
        tag: "text",
        content: "webgl - animation - groups"
      }
    ]
  ],
  init: ({
    window,
    canvas,
    GUI,
    Stats,
    needToDispose,
    useFrame
  }) => {
    let stats, clock;
    let scene, camera, renderer, mixer;
    init();
    function init() {
      scene = new common_vendor.Scene();
      camera = new common_vendor.PerspectiveCamera(40, window.innerWidth / window.innerHeight, 1, 1e3);
      camera.position.set(50, 50, 100);
      camera.lookAt(scene.position);
      const animationGroup = new common_vendor.AnimationObjectGroup();
      const geometry = new common_vendor.BoxGeometry(5, 5, 5);
      const material = new common_vendor.MeshBasicMaterial({
        transparent: true
      });
      for (let i = 0; i < 5; i++) {
        for (let j = 0; j < 5; j++) {
          const mesh = new common_vendor.Mesh(geometry, material);
          mesh.position.x = 32 - 16 * i;
          mesh.position.y = 0;
          mesh.position.z = 32 - 16 * j;
          scene.add(mesh);
          animationGroup.add(mesh);
        }
      }
      const xAxis = new common_vendor.Vector3(1, 0, 0);
      const qInitial = new common_vendor.Quaternion().setFromAxisAngle(xAxis, 0);
      const qFinal = new common_vendor.Quaternion().setFromAxisAngle(xAxis, Math.PI);
      const quaternionKF = new common_vendor.QuaternionKeyframeTrack(
        ".quaternion",
        [0, 1, 2],
        [
          qInitial.x,
          qInitial.y,
          qInitial.z,
          qInitial.w,
          qFinal.x,
          qFinal.y,
          qFinal.z,
          qFinal.w,
          qInitial.x,
          qInitial.y,
          qInitial.z,
          qInitial.w
        ]
      );
      const colorKF = new common_vendor.ColorKeyframeTrack(
        ".material.color",
        [0, 1, 2],
        [1, 0, 0, 0, 1, 0, 0, 0, 1],
        common_vendor.InterpolateDiscrete
      );
      const opacityKF = new common_vendor.NumberKeyframeTrack(".material.opacity", [0, 1, 2], [1, 0, 1]);
      const clip = new common_vendor.AnimationClip("default", 3, [quaternionKF, colorKF, opacityKF]);
      mixer = new common_vendor.AnimationMixer(animationGroup);
      const clipAction = mixer.clipAction(clip);
      clipAction.play();
      renderer = new common_vendor.WebGLRenderer({
        antialias: true,
        canvas
      });
      renderer.setPixelRatio(window.devicePixelRatio);
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setAnimationLoop(animate);
      stats = new Stats(renderer);
      clock = new common_vendor.Clock();
      window.addEventListener("resize", onWindowResize);
      needToDispose(renderer, scene);
    }
    function onWindowResize() {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    }
    function animate() {
      const delta = clock.getDelta();
      if (mixer) {
        mixer.update(delta);
      }
      renderer.render(scene, camera);
      stats.update();
    }
  }
};
const _sfc_main = exampleInfo;
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => _ctx.testFunction && _ctx.testFunction(...args)),
    b: common_vendor.t(_ctx.platform)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
