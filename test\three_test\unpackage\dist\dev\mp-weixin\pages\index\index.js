"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      message: "Animation Groups 演示",
      canvas: null,
      scene: null,
      camera: null,
      renderer: null,
      mixer: null,
      clock: null,
      animationId: null,
      animationTime: 0,
      cubes: []
    };
  },
  onLoad() {
    console.log("页面onLoad执行");
  },
  onReady() {
    console.log("页面onReady执行");
    console.log("当前平台:", common_vendor.index.getSystemInfoSync().platform);
    setTimeout(() => {
      this.initWebGL();
    }, 500);
  },
  onUnload() {
    if (this.animationId) {
      clearTimeout(this.animationId);
    }
  },
  methods: {
    initWebGL() {
      console.log("开始初始化Animation Groups演示");
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select("#webglCanvas").fields({
        node: true,
        size: true
      }).exec((res) => {
        console.log("Canvas查询结果:", res);
        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          console.log("Canvas节点获取成功:", canvas);
          const gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
          if (gl) {
            console.log("WebGL上下文获取成功!");
            this.canvas = canvas;
            this.setupThreeJS();
          } else {
            console.error("无法获取WebGL上下文");
            this.fallbackTo2D(canvas);
          }
        } else {
          console.error("无法找到canvas节点");
        }
      });
    },
    setupThreeJS() {
      console.log("设置Three.js动画组场景");
      console.log("微信小程序环境不支持three.js，回退到2D模拟");
      this.fallbackTo2D(this.canvas);
    },
    createAnimationGroup(THREE) {
      console.log("创建动画组");
      const animationGroup = new THREE.AnimationObjectGroup();
      const geometry = new THREE.BoxGeometry(5, 5, 5);
      const material = new THREE.MeshBasicMaterial({ transparent: true });
      for (let i = 0; i < 5; i++) {
        for (let j = 0; j < 5; j++) {
          const mesh = new THREE.Mesh(geometry, material);
          mesh.position.x = 32 - 16 * i;
          mesh.position.y = 0;
          mesh.position.z = 32 - 16 * j;
          this.scene.add(mesh);
          animationGroup.add(mesh);
        }
      }
      const xAxis = new THREE.Vector3(1, 0, 0);
      const qInitial = new THREE.Quaternion().setFromAxisAngle(xAxis, 0);
      const qFinal = new THREE.Quaternion().setFromAxisAngle(xAxis, Math.PI);
      const quaternionKF = new THREE.QuaternionKeyframeTrack(
        ".quaternion",
        [0, 1, 2],
        [
          qInitial.x,
          qInitial.y,
          qInitial.z,
          qInitial.w,
          qFinal.x,
          qFinal.y,
          qFinal.z,
          qFinal.w,
          qInitial.x,
          qInitial.y,
          qInitial.z,
          qInitial.w
        ]
      );
      const colorKF = new THREE.ColorKeyframeTrack(
        ".material.color",
        [0, 1, 2],
        [1, 0, 0, 0, 1, 0, 0, 0, 1],
        THREE.InterpolateDiscrete
      );
      const opacityKF = new THREE.NumberKeyframeTrack(".material.opacity", [0, 1, 2], [1, 0, 1]);
      const clip = new THREE.AnimationClip("default", 3, [quaternionKF, colorKF, opacityKF]);
      this.mixer = new THREE.AnimationMixer(animationGroup);
      const clipAction = this.mixer.clipAction(clip);
      clipAction.play();
      console.log("动画组创建完成，包含25个立方体");
    },
    animate() {
      if (!this.renderer || !this.scene || !this.camera)
        return;
      const delta = this.clock.getDelta();
      if (this.mixer) {
        this.mixer.update(delta);
      }
      this.renderer.render(this.scene, this.camera);
      this.animationId = setTimeout(() => this.animate(), 16);
    },
    fallbackTo2D(canvas) {
      console.log("开始2D Canvas模拟Animation Groups");
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        console.error("无法获取2D上下文");
        return;
      }
      const dpr = common_vendor.index.getSystemInfoSync().pixelRatio;
      canvas.width = 300 * dpr;
      canvas.height = 300 * dpr;
      ctx.scale(dpr, dpr);
      this.animationTime = 0;
      this.cubes = [];
      for (let i = 0; i < 5; i++) {
        for (let j = 0; j < 5; j++) {
          this.cubes.push({
            x: 150 + (i - 2) * 40,
            // 中心化位置
            y: 150 + (j - 2) * 40,
            size: 20,
            rotation: 0,
            color: { r: 1, g: 0, b: 0 },
            opacity: 1
          });
        }
      }
      this.animate2D(ctx);
      console.log("2D Animation Groups模拟启动，包含25个方块");
    },
    animate2D(ctx) {
      if (!ctx)
        return;
      this.animationTime += 0.016;
      const t = this.animationTime % 3 / 3;
      ctx.fillStyle = "#000";
      ctx.fillRect(0, 0, 300, 300);
      this.cubes.forEach((cube) => {
        const rotationPhase = t * Math.PI * 2;
        const colorPhase = t * 3;
        const opacityPhase = Math.abs(Math.sin(t * Math.PI));
        cube.rotation = rotationPhase;
        if (colorPhase < 1) {
          cube.color = { r: 1 - colorPhase, g: colorPhase, b: 0 };
        } else if (colorPhase < 2) {
          cube.color = { r: 0, g: 2 - colorPhase, b: colorPhase - 1 };
        } else {
          cube.color = { r: colorPhase - 2, g: 0, b: 3 - colorPhase };
        }
        cube.opacity = opacityPhase;
        this.drawCube2D(ctx, cube);
      });
      ctx.fillStyle = "#fff";
      ctx.font = "12px Arial";
      ctx.textAlign = "left";
      ctx.fillText("2D模拟 Animation Groups", 10, 20);
      ctx.fillText("25个方块同步动画", 10, 35);
      ctx.fillText(`时间: ${this.animationTime.toFixed(1)}s`, 10, 290);
      this.animationId = setTimeout(() => this.animate2D(ctx), 16);
    },
    drawCube2D(ctx, cube) {
      ctx.save();
      ctx.translate(cube.x, cube.y);
      ctx.rotate(cube.rotation);
      const r = Math.floor(cube.color.r * 255);
      const g = Math.floor(cube.color.g * 255);
      const b = Math.floor(cube.color.b * 255);
      const alpha = cube.opacity;
      ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${alpha})`;
      ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${Math.min(alpha + 0.3, 1)})`;
      ctx.lineWidth = 1;
      const halfSize = cube.size / 2;
      ctx.fillRect(-halfSize, -halfSize, cube.size, cube.size);
      ctx.strokeRect(-halfSize, -halfSize, cube.size, cube.size);
      ctx.beginPath();
      ctx.moveTo(-halfSize, -halfSize);
      ctx.lineTo(halfSize, halfSize);
      ctx.moveTo(halfSize, -halfSize);
      ctx.lineTo(-halfSize, halfSize);
      ctx.stroke();
      ctx.restore();
    },
    onTouchStart() {
      console.log("触摸开始");
    },
    onTouchMove() {
      console.log("触摸移动");
    },
    onTouchEnd() {
      console.log("触摸结束");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    b: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    c: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
