<template>
	<view class="container">
		<view class="head-line">
			<text>Animation Groups 演示</text>
		</view>
		<canvas
			class="webgl-canvas"
			type="webgl"
			id="webglCanvas"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd">
		</canvas>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				message: 'Animation Groups 演示',
				canvas: null,
				scene: null,
				camera: null,
				renderer: null,
				mixer: null,
				clock: null,
				animationId: null,
				animationTime: 0,
				cubes: []
			}
		},
		onLoad() {
			console.log('页面onLoad执行');
		},
		onReady() {
			console.log('页面onReady执行');
			console.log('当前平台:', uni.getSystemInfoSync().platform);

			// 尝试初始化WebGL
			setTimeout(() => {
				this.initWebGL();
			}, 500);
		},
		onUnload() {
			// 清理动画
			if (this.animationId) {
				clearTimeout(this.animationId);
			}
		},
		methods: {
			initWebGL() {
				console.log('开始初始化Animation Groups演示');

				// 获取canvas节点
				const query = uni.createSelectorQuery().in(this);
				query.select('#webglCanvas').fields({
					node: true,
					size: true
				}).exec((res) => {
					console.log('Canvas查询结果:', res);
					if (res[0] && res[0].node) {
						const canvas = res[0].node;
						console.log('Canvas节点获取成功:', canvas);

						// 尝试获取WebGL上下文
						const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

						if (gl) {
							console.log('WebGL上下文获取成功!');
							this.canvas = canvas;
							this.setupThreeJS();
						} else {
							console.error('无法获取WebGL上下文');
							this.fallbackTo2D(canvas);
						}
					} else {
						console.error('无法找到canvas节点');
					}
				});
			},

			setupThreeJS() {
				console.log('设置Three.js动画组场景');
				console.log('微信小程序环境不支持three.js，回退到2D模拟');
				this.fallbackTo2D(this.canvas);
			},

			createAnimationGroup(THREE) {
				console.log('创建动画组');

				// 创建动画对象组
				const animationGroup = new THREE.AnimationObjectGroup();

				// 创建几何体和材质
				const geometry = new THREE.BoxGeometry(5, 5, 5);
				const material = new THREE.MeshBasicMaterial({ transparent: true });

				// 创建5x5网格的立方体
				for (let i = 0; i < 5; i++) {
					for (let j = 0; j < 5; j++) {
						const mesh = new THREE.Mesh(geometry, material);
						mesh.position.x = 32 - 16 * i;
						mesh.position.y = 0;
						mesh.position.z = 32 - 16 * j;
						this.scene.add(mesh);
						animationGroup.add(mesh);
					}
				}

				// 创建旋转动画轨道
				const xAxis = new THREE.Vector3(1, 0, 0);
				const qInitial = new THREE.Quaternion().setFromAxisAngle(xAxis, 0);
				const qFinal = new THREE.Quaternion().setFromAxisAngle(xAxis, Math.PI);
				const quaternionKF = new THREE.QuaternionKeyframeTrack(
					".quaternion",
					[0, 1, 2],
					[
						qInitial.x, qInitial.y, qInitial.z, qInitial.w,
						qFinal.x, qFinal.y, qFinal.z, qFinal.w,
						qInitial.x, qInitial.y, qInitial.z, qInitial.w
					]
				);

				// 创建颜色动画轨道
				const colorKF = new THREE.ColorKeyframeTrack(
					".material.color",
					[0, 1, 2],
					[1, 0, 0, 0, 1, 0, 0, 0, 1],
					THREE.InterpolateDiscrete
				);

				// 创建透明度动画轨道
				const opacityKF = new THREE.NumberKeyframeTrack(".material.opacity", [0, 1, 2], [1, 0, 1]);

				// 创建动画剪辑
				const clip = new THREE.AnimationClip("default", 3, [quaternionKF, colorKF, opacityKF]);

				// 创建动画混合器
				this.mixer = new THREE.AnimationMixer(animationGroup);
				const clipAction = this.mixer.clipAction(clip);
				clipAction.play();

				console.log('动画组创建完成，包含25个立方体');
			},

			animate() {
				if (!this.renderer || !this.scene || !this.camera) return;

				const delta = this.clock.getDelta();
				if (this.mixer) {
					this.mixer.update(delta);
				}

				this.renderer.render(this.scene, this.camera);

				// 使用setTimeout代替requestAnimationFrame
				this.animationId = setTimeout(() => this.animate(), 16);
			},

			fallbackTo2D(canvas) {
				console.log('开始2D Canvas模拟Animation Groups');

				// 获取2D上下文
				const ctx = canvas.getContext('2d');
				if (!ctx) {
					console.error('无法获取2D上下文');
					return;
				}

				// 设置canvas尺寸
				const dpr = uni.getSystemInfoSync().pixelRatio;
				canvas.width = 300 * dpr;
				canvas.height = 300 * dpr;
				ctx.scale(dpr, dpr);

				// 初始化动画参数
				this.animationTime = 0;
				this.cubes = [];

				// 创建5x5网格的立方体数据
				for (let i = 0; i < 5; i++) {
					for (let j = 0; j < 5; j++) {
						this.cubes.push({
							x: 150 + (i - 2) * 40,  // 中心化位置
							y: 150 + (j - 2) * 40,
							size: 20,
							rotation: 0,
							color: { r: 1, g: 0, b: 0 },
							opacity: 1
						});
					}
				}

				// 开始2D动画
				this.animate2D(ctx);
				console.log('2D Animation Groups模拟启动，包含25个方块');
			},

			animate2D(ctx) {
				if (!ctx) return;

				// 更新动画时间
				this.animationTime += 0.016; // 约60fps
				const t = (this.animationTime % 3) / 3; // 3秒循环，归一化到0-1

				// 清除画布
				ctx.fillStyle = '#000';
				ctx.fillRect(0, 0, 300, 300);

				// 更新并绘制每个立方体
				this.cubes.forEach(cube => {
					// 计算动画值
					const rotationPhase = t * Math.PI * 2; // 完整旋转
					const colorPhase = t * 3; // 颜色循环
					const opacityPhase = Math.abs(Math.sin(t * Math.PI)); // 透明度变化

					// 更新立方体属性
					cube.rotation = rotationPhase;

					// 颜色动画：红→绿→蓝
					if (colorPhase < 1) {
						cube.color = { r: 1 - colorPhase, g: colorPhase, b: 0 };
					} else if (colorPhase < 2) {
						cube.color = { r: 0, g: 2 - colorPhase, b: colorPhase - 1 };
					} else {
						cube.color = { r: colorPhase - 2, g: 0, b: 3 - colorPhase };
					}

					cube.opacity = opacityPhase;

					// 绘制立方体
					this.drawCube2D(ctx, cube);
				});

				// 绘制信息
				ctx.fillStyle = '#fff';
				ctx.font = '12px Arial';
				ctx.textAlign = 'left';
				ctx.fillText('2D模拟 Animation Groups', 10, 20);
				ctx.fillText('25个方块同步动画', 10, 35);
				ctx.fillText(`时间: ${this.animationTime.toFixed(1)}s`, 10, 290);

				// 继续动画
				this.animationId = setTimeout(() => this.animate2D(ctx), 16);
			},

			drawCube2D(ctx, cube) {
				ctx.save();

				// 移动到立方体中心
				ctx.translate(cube.x, cube.y);
				ctx.rotate(cube.rotation);

				// 设置颜色和透明度
				const r = Math.floor(cube.color.r * 255);
				const g = Math.floor(cube.color.g * 255);
				const b = Math.floor(cube.color.b * 255);
				const alpha = cube.opacity;

				ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${alpha})`;
				ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${Math.min(alpha + 0.3, 1)})`;
				ctx.lineWidth = 1;

				// 绘制正方形（模拟立方体）
				const halfSize = cube.size / 2;
				ctx.fillRect(-halfSize, -halfSize, cube.size, cube.size);
				ctx.strokeRect(-halfSize, -halfSize, cube.size, cube.size);

				// 绘制对角线（增加3D感）
				ctx.beginPath();
				ctx.moveTo(-halfSize, -halfSize);
				ctx.lineTo(halfSize, halfSize);
				ctx.moveTo(halfSize, -halfSize);
				ctx.lineTo(-halfSize, halfSize);
				ctx.stroke();

				ctx.restore();
			},

			onTouchStart() {
				console.log('触摸开始');
			},

			onTouchMove() {
				console.log('触摸移动');
			},

			onTouchEnd() {
				console.log('触摸结束');
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
	}

	.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
	}

	.webgl-canvas {
		flex: 1;
		width: 100%;
		min-height: 400px;
		background-color: #000;
		position: relative;
	}
</style>