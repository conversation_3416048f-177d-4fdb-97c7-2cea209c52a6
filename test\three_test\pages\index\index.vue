<template>
	<view class="container">
		<view class="head-line">
			<text>Animation Groups 演示</text>
		</view>
		<canvas
			class="webgl-canvas"
			type="webgl"
			id="webglCanvas"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd">
		</canvas>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				message: 'Animation Groups 演示',
				canvas: null,
				scene: null,
				camera: null,
				renderer: null,
				mixer: null,
				clock: null,
				animationId: null
			}
		},
		onLoad() {
			console.log('页面onLoad执行');
		},
		onReady() {
			console.log('页面onReady执行');
			console.log('当前平台:', uni.getSystemInfoSync().platform);

			// 尝试初始化WebGL
			setTimeout(() => {
				this.initWebGL();
			}, 500);
		},
		onUnload() {
			// 清理动画
			if (this.animationId) {
				clearTimeout(this.animationId);
			}
		},
		methods: {
			initWebGL() {
				console.log('开始初始化Animation Groups演示');

				// 获取canvas节点
				const query = uni.createSelectorQuery().in(this);
				query.select('#webglCanvas').fields({
					node: true,
					size: true
				}).exec((res) => {
					console.log('Canvas查询结果:', res);
					if (res[0] && res[0].node) {
						const canvas = res[0].node;
						console.log('Canvas节点获取成功:', canvas);

						// 尝试获取WebGL上下文
						const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

						if (gl) {
							console.log('WebGL上下文获取成功!');
							this.canvas = canvas;
							this.setupThreeJS();
						} else {
							console.error('无法获取WebGL上下文');
							this.fallbackTo2D(canvas);
						}
					} else {
						console.error('无法找到canvas节点');
					}
				});
			},

			setupThreeJS() {
				console.log('设置Three.js动画组场景');

				try {
					// 动态导入three.js
					const THREE = require('three');

					const canvas = this.canvas;
					const dpr = uni.getSystemInfoSync().pixelRatio;
					const width = 300 * dpr;
					const height = 300 * dpr;

					// 设置canvas尺寸
					canvas.width = width;
					canvas.height = height;

					// 创建场景
					this.scene = new THREE.Scene();
					this.scene.background = new THREE.Color(0x000000);

					// 创建相机
					this.camera = new THREE.PerspectiveCamera(40, width / height, 1, 1000);
					this.camera.position.set(50, 50, 100);
					this.camera.lookAt(this.scene.position);

					// 创建渲染器
					this.renderer = new THREE.WebGLRenderer({
						canvas: canvas,
						antialias: true
					});
					this.renderer.setSize(width, height, false);

					// 创建动画组
					this.createAnimationGroup(THREE);

					// 创建时钟
					this.clock = new THREE.Clock();

					// 开始动画循环
					this.animate();

					console.log('Three.js动画组场景设置完成');
				} catch (error) {
					console.error('Three.js设置失败:', error);
					this.fallbackTo2D(this.canvas);
				}
			},

			createAnimationGroup(THREE) {
				console.log('创建动画组');

				// 创建动画对象组
				const animationGroup = new THREE.AnimationObjectGroup();

				// 创建几何体和材质
				const geometry = new THREE.BoxGeometry(5, 5, 5);
				const material = new THREE.MeshBasicMaterial({ transparent: true });

				// 创建5x5网格的立方体
				for (let i = 0; i < 5; i++) {
					for (let j = 0; j < 5; j++) {
						const mesh = new THREE.Mesh(geometry, material);
						mesh.position.x = 32 - 16 * i;
						mesh.position.y = 0;
						mesh.position.z = 32 - 16 * j;
						this.scene.add(mesh);
						animationGroup.add(mesh);
					}
				}

				// 创建旋转动画轨道
				const xAxis = new THREE.Vector3(1, 0, 0);
				const qInitial = new THREE.Quaternion().setFromAxisAngle(xAxis, 0);
				const qFinal = new THREE.Quaternion().setFromAxisAngle(xAxis, Math.PI);
				const quaternionKF = new THREE.QuaternionKeyframeTrack(
					".quaternion",
					[0, 1, 2],
					[
						qInitial.x, qInitial.y, qInitial.z, qInitial.w,
						qFinal.x, qFinal.y, qFinal.z, qFinal.w,
						qInitial.x, qInitial.y, qInitial.z, qInitial.w
					]
				);

				// 创建颜色动画轨道
				const colorKF = new THREE.ColorKeyframeTrack(
					".material.color",
					[0, 1, 2],
					[1, 0, 0, 0, 1, 0, 0, 0, 1],
					THREE.InterpolateDiscrete
				);

				// 创建透明度动画轨道
				const opacityKF = new THREE.NumberKeyframeTrack(".material.opacity", [0, 1, 2], [1, 0, 1]);

				// 创建动画剪辑
				const clip = new THREE.AnimationClip("default", 3, [quaternionKF, colorKF, opacityKF]);

				// 创建动画混合器
				this.mixer = new THREE.AnimationMixer(animationGroup);
				const clipAction = this.mixer.clipAction(clip);
				clipAction.play();

				console.log('动画组创建完成，包含25个立方体');
			},

			animate() {
				if (!this.renderer || !this.scene || !this.camera) return;

				const delta = this.clock.getDelta();
				if (this.mixer) {
					this.mixer.update(delta);
				}

				this.renderer.render(this.scene, this.camera);

				// 使用setTimeout代替requestAnimationFrame
				this.animationId = setTimeout(() => this.animate(), 16);
			},

			fallbackTo2D(canvas) {
				console.log('回退到2D Canvas演示');
				const ctx = canvas.getContext('2d');
				if (ctx) {
					// 设置canvas尺寸
					const dpr = uni.getSystemInfoSync().pixelRatio;
					canvas.width = 300 * dpr;
					canvas.height = 300 * dpr;
					ctx.scale(dpr, dpr);

					// 绘制提示信息
					ctx.fillStyle = '#000';
					ctx.fillRect(0, 0, 300, 300);

					ctx.fillStyle = '#fff';
					ctx.font = '16px Arial';
					ctx.textAlign = 'center';
					ctx.fillText('WebGL不可用', 150, 130);
					ctx.fillText('Animation Groups演示', 150, 150);
					ctx.fillText('需要WebGL支持', 150, 170);

					console.log('2D Canvas回退演示完成');
				}
			},

			onTouchStart(e) {
				console.log('触摸开始');
			},

			onTouchMove(e) {
				console.log('触摸移动');
			},

			onTouchEnd(e) {
				console.log('触摸结束');
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
	}

	.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
	}

	.content-area {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
	}

	.title {
		font-size: 32rpx;
		margin-bottom: 40rpx;
		color: #333;
	}

	button {
		background-color: #007aff;
		color: white;
		border: none;
		padding: 24rpx 48rpx;
		border-radius: 12rpx;
		margin: 20rpx 0;
		font-size: 28rpx;
	}

	.info-box {
		margin-top: 40rpx;
		padding: 30rpx;
		background-color: white;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.info {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 16rpx;
		text-align: center;
		display: block;
	}
</style>