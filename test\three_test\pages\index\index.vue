<template>
	<view class="container">
		<view class="head-line">
			<text>Teacher模型演示</text>
		</view>
		<view class="content-area">
			<text class="title">基础功能测试</text>
			<button @click="testFunction">点击测试</button>
			<view class="info-box">
				<text class="info">当前平台: {{platform}}</text>
				<text class="info">renderjs在微信小程序中可能不支持</text>
				<text class="info">建议在APP环境中测试3D功能</text>
			</view>
		</view>
	</view>
</template>

<script>
	import * as THREE from "three";

	/** @type {import("@minisheeep/mp-three-examples").OfficialExampleInfo} */
	const exampleInfo = {
		name: "misc_animation_groups",
		useLoaders: [],
		info: [
			[{
					tag: "a",
					link: "https://threejs.org",
					content: "three.js"
				},
				{
					tag: "text",
					content: "webgl - animation - groups"
				}
			]
		],
		init: ({
			window,
			canvas,
			GUI,
			Stats,
			needToDispose,
			useFrame
		}) => {
			let stats, clock;
			let scene, camera, renderer, mixer;
			init();

			function init() {
				scene = new THREE.Scene();
				camera = new THREE.PerspectiveCamera(40, window.innerWidth / window.innerHeight, 1, 1e3);
				camera.position.set(50, 50, 100);
				camera.lookAt(scene.position);
				const animationGroup = new THREE.AnimationObjectGroup();
				const geometry = new THREE.BoxGeometry(5, 5, 5);
				const material = new THREE.MeshBasicMaterial({
					transparent: true
				});
				for (let i = 0; i < 5; i++) {
					for (let j = 0; j < 5; j++) {
						const mesh = new THREE.Mesh(geometry, material);
						mesh.position.x = 32 - 16 * i;
						mesh.position.y = 0;
						mesh.position.z = 32 - 16 * j;
						scene.add(mesh);
						animationGroup.add(mesh);
					}
				}
				const xAxis = new THREE.Vector3(1, 0, 0);
				const qInitial = new THREE.Quaternion().setFromAxisAngle(xAxis, 0);
				const qFinal = new THREE.Quaternion().setFromAxisAngle(xAxis, Math.PI);
				const quaternionKF = new THREE.QuaternionKeyframeTrack(
					".quaternion",
					[0, 1, 2],
					[
						qInitial.x,
						qInitial.y,
						qInitial.z,
						qInitial.w,
						qFinal.x,
						qFinal.y,
						qFinal.z,
						qFinal.w,
						qInitial.x,
						qInitial.y,
						qInitial.z,
						qInitial.w
					]
				);
				const colorKF = new THREE.ColorKeyframeTrack(
					".material.color",
					[0, 1, 2],
					[1, 0, 0, 0, 1, 0, 0, 0, 1],
					THREE.InterpolateDiscrete
				);
				const opacityKF = new THREE.NumberKeyframeTrack(".material.opacity", [0, 1, 2], [1, 0, 1]);
				const clip = new THREE.AnimationClip("default", 3, [quaternionKF, colorKF, opacityKF]);
				mixer = new THREE.AnimationMixer(animationGroup);
				const clipAction = mixer.clipAction(clip);
				clipAction.play();
				renderer = new THREE.WebGLRenderer({
					antialias: true,
					canvas
				});
				renderer.setPixelRatio(window.devicePixelRatio);
				renderer.setSize(window.innerWidth, window.innerHeight);
				renderer.setAnimationLoop(animate);
				stats = new Stats(renderer);
				clock = new THREE.Clock();
				window.addEventListener("resize", onWindowResize);
				needToDispose(renderer, scene);
			}

			function onWindowResize() {
				camera.aspect = window.innerWidth / window.innerHeight;
				camera.updateProjectionMatrix();
				renderer.setSize(window.innerWidth, window.innerHeight);
			}

			function animate() {
				const delta = clock.getDelta();
				if (mixer) {
					mixer.update(delta);
				}
				renderer.render(scene, camera);
				stats.update();
			}
		}
	};
	export {
		exampleInfo as
		default
	};
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
	}

	.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
	}

	.content-area {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
	}

	.title {
		font-size: 32rpx;
		margin-bottom: 40rpx;
		color: #333;
	}

	button {
		background-color: #007aff;
		color: white;
		border: none;
		padding: 24rpx 48rpx;
		border-radius: 12rpx;
		margin: 20rpx 0;
		font-size: 28rpx;
	}

	.info-box {
		margin-top: 40rpx;
		padding: 30rpx;
		background-color: white;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.info {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 16rpx;
		text-align: center;
		display: block;
	}
</style>